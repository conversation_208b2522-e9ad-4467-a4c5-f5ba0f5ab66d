{"name": "@unrup1-gaerschrank/backend", "version": "0.1.1", "author": {"name": "<PERSON>", "url": "https://philun.de"}, "license": "UNLICENSED", "engines": {"yarn": "1.22.x"}, "private": true, "scripts": {"dev": "tsx watch ./src/app.ts", "build": "rm -rf ./dist && prisma generate && tsc", "watch": "tsx watch", "prisma": "prisma", "test": "jest", "test:watch": "jest --watch", "prod": "node ./dist/app.js", "web-push": "web-push generate-vapid-keys", "download": "tsx prisma/download.ts", "seed": "tsx prisma/seed.ts", "dev:nanomq": "docker cp docker/mqtt/nanomq.conf unrup1-gaerschrank-backend-mqtt:/etc/nanomq.conf", "dev:nanomq:restart": "docker exec -it unrup1-gaerschrank-backend-mqtt /bin/sh -c 'nanomq restart'", "lint": "yarn run lint:script & yarn run lint:pretty", "lint:script": "eslint ./src/**/*.ts", "lint:pretty": "prettier ./src/**/*.ts --check"}, "prisma": {"seed": "tsx prisma/seed.ts", "schema": "./prisma"}, "dependencies": {"@hono/node-server": "^1.8.2", "@hono/zod-validator": "^0.7.0", "@lucia-auth/adapter-prisma": "^4.0.1", "@node-rs/argon2": "^1.8.3", "@prisma/client": "^6.12.0", "@sentry/node": "^9.40.0", "@simplewebauthn/server": "^13.1.2", "@simplewebauthn/types": "^12.0.0", "@tuya/tuya-connector-nodejs": "^2.1.2", "cheerio": "^1.0.0-rc.12", "cors": "^2.8.5", "hono": "^4.1.3", "i18next": "^25.2.0", "lucia": "^3.2.0", "luxon": "^3.4.4", "node-cache": "^5.1.2", "node-schedule": "^2.1.1", "openai": "^5.2.0", "oslo": "^1.2.1", "pdf-parse": "^1.1.1", "shortid": "^2.2.16", "tsx": "^4.19.1", "web-push": "^3.6.6", "ws": "^8.18.0", "zod": "^3.22.4"}, "devDependencies": {"@faker-js/faker": "^9.4.0", "@types/cors": "^2.8.17", "@types/jest": "^30.0.0", "@types/luxon": "^3.3.2", "@types/node": "^20.8.6", "@types/node-schedule": "^2.1.5", "@types/shortid": "^2.2.0", "@types/supertest": "^6.0.2", "@types/web-push": "^3.6.3", "jest": "^30.0.0", "jest-mock-extended": "4.0.0", "prisma": "^6.12.0", "superagent": "^10.2.2", "supertest": "^7.0.0", "ts-jest": "^29.1.1", "typescript": "^5.4.4", "zod-prisma": "^0.5.4"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}