// Simple test to verify WebAuthn routes are working
const http = require('http');

const testEndpoints = [
  { path: '/api/auth/webauthn/register/begin', method: 'POST' },
  { path: '/api/auth/webauthn/register/finish', method: 'POST' },
  { path: '/api/auth/webauthn/authenticate/begin', method: 'POST' },
  { path: '/api/auth/webauthn/authenticate/finish', method: 'POST' }
];

function testEndpoint(endpoint) {
  return new Promise((resolve) => {
    const postData = JSON.stringify({ email: '<EMAIL>' });
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: endpoint.path,
      method: endpoint.method,
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        console.log(`${endpoint.method} ${endpoint.path}: ${res.statusCode}`);
        if (res.statusCode !== 200) {
          console.log(`Response: ${data}`);
        }
        resolve();
      });
    });

    req.on('error', (e) => {
      console.log(`${endpoint.method} ${endpoint.path}: ERROR - ${e.message}`);
      resolve();
    });

    req.write(postData);
    req.end();
  });
}

async function runTests() {
  console.log('Testing WebAuthn endpoints...\n');
  
  for (const endpoint of testEndpoints) {
    await testEndpoint(endpoint);
  }
  
  console.log('\nTest completed!');
}

// Wait a bit for server to start, then run tests
setTimeout(runTests, 2000);
