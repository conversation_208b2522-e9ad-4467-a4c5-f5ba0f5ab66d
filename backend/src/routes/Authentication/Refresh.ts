import { getLucia } from '../../integration/lucia'
import { app } from '../../helpers/hono'
import prisma from '../../helpers/prisma'

app.post('/auth/refresh', async c => {
  try {
    // Get the current session from the Authorization header
    const header = c.req.header('Authentication')
    if (!header) {
      return c.json({ error: 'No authentication header found' }, 401)
    }

    const lucia = await getLucia()
    const sessionId = lucia.readBearerToken(header)

    if (!sessionId) {
      return c.json({ error: 'Invalid session token' }, 401)
    }

    // Validate the current session
    const { session, user } = await lucia.validateSession(sessionId)

    if (!session || !user) {
      return c.json({ error: 'Session not valid or expired' }, 401)
    }

    // Create a new session for the user (this effectively "refreshes" the session)
    await lucia.invalidateSession(sessionId)
    const newSession = await lucia.createSession(user.id, {})

    // Update user's last login time
    await prisma.user.update({
      where: {
        id: user.id,
      },
      data: {
        lastLogin: new Date(),
      },
    })

    // Calculate expiration time (Lucia sessions typically expire in 30 days)
    const expiresAt = Date.now() + 30 * 24 * 60 * 60 * 1000 // 30 days in milliseconds

    return c.json({
      token: newSession.id,
      refreshToken: newSession.id, // In Lucia, the session ID can serve as both access and refresh token
      expiresAt: expiresAt,
    })
  } catch (error) {
    console.error('Token refresh error:', error)
    return c.json({ error: 'Token refresh failed' }, 500)
  }
})
