import { app } from '../../helpers/hono'
import prisma from '../../helpers/prisma'
import { getLucia } from '../../integration/lucia'
import {
  generateRegistrationOptions,
  verifyRegistrationResponse,
  generateAuthenticationOptions,
  verifyAuthenticationResponse,
} from '@simplewebauthn/server'

import type {
  RegistrationResponseJSON,
  AuthenticationResponseJSON,
  AuthenticatorTransport,
} from '@simplewebauthn/types'

// WebAuthn configuration
const rpName = 'Gaerschrank'
const rpID = process.env.WEBAUTHN_RP_ID || 'localhost'
const origin = process.env.WEBAUTHN_ORIGIN || 'http://localhost:3000'

// Store challenges temporarily (in production, use Redis or database)
const challenges = new Map<string, string>()

// Helper function to clean up old challenges
const cleanupOldChallenges = () => {
  const now = Date.now()
  for (const [key, value] of challenges.entries()) {
    const [challenge, timestamp] = value.split('|')
    if (now - parseInt(timestamp) > 5 * 60 * 1000) {
      // 5 minutes
      challenges.delete(key)
    }
  }
}

// Registration endpoints
app.post('/auth/webauthn/register/begin', async c => {
  try {
    const body = (await c.req.json()) as { email: string }

    if (!body.email) {
      return c.json({ error: 'Email is required' }, 400)
    }

    // Check if user exists
    let user = await prisma.user.findUnique({
      where: { email: body.email },
      include: { WebAuthnCredential: true },
    })

    // If user doesn't exist, create them (for registration flow)
    if (!user) {
      user = await prisma.user.create({
        data: {
          email: body.email,
          password: '', // Empty password for WebAuthn-only users
          name: body.email.split('@')[0],
          isEmailVerified: false,
        },
        include: { WebAuthnCredential: true },
      })
    }

    const userCredentials = user.WebAuthnCredential.map(cred => ({
      id: cred.credentialId,
      type: 'public-key' as const,
    }))

    const options = await generateRegistrationOptions({
      rpName,
      rpID,
      userID: new TextEncoder().encode(user.id),
      userName: user.email,
      userDisplayName: user.name || user.email,
      attestationType: 'none',
      excludeCredentials: userCredentials,
      authenticatorSelection: {
        residentKey: 'preferred',
        userVerification: 'preferred',
        authenticatorAttachment: 'platform',
      },
    })

    // Store challenge temporarily
    challenges.set(user.email, `${options.challenge}|${Date.now()}`)
    cleanupOldChallenges()

    return c.json(options)
  } catch (error) {
    console.error('WebAuthn registration begin error:', error)
    return c.json({ error: 'Failed to generate registration options' }, 500)
  }
})

app.post('/auth/webauthn/register/finish', async c => {
  try {
    const body = (await c.req.json()) as {
      email: string
      registrationResponse: RegistrationResponseJSON
    }

    if (!body.email || !body.registrationResponse) {
      return c.json({ error: 'Email and registration response are required' }, 400)
    }

    // Get stored challenge
    const challengeData = challenges.get(body.email)
    if (!challengeData) {
      return c.json({ error: 'No challenge found for this email' }, 400)
    }

    const [expectedChallenge] = challengeData.split('|')

    // Find user
    const user = await prisma.user.findUnique({
      where: { email: body.email },
      include: { WebAuthnCredential: true },
    })

    if (!user) {
      return c.json({ error: 'User not found' }, 404)
    }

    // Verify registration response
    const verification = await verifyRegistrationResponse({
      response: body.registrationResponse,
      expectedChallenge,
      expectedOrigin: origin,
      expectedRPID: rpID,
    })

    if (!verification.verified || !verification.registrationInfo) {
      return c.json({ error: 'Registration verification failed' }, 400)
    }

    const { credential } = verification.registrationInfo

    // Save credential to database
    await prisma.webAuthnCredential.create({
      data: {
        credentialId: Buffer.from(credential.id).toString('base64url'),
        publicKey: Buffer.from(credential.publicKey),
        counter: BigInt(credential.counter),
        deviceType: 'singleDevice', // Default value
        backedUp: false, // Default value
        transports: JSON.stringify(body.registrationResponse.response.transports || []),
        userId: user.id,
      },
    })

    // Clean up challenge
    challenges.delete(body.email)

    // Create session
    const lucia = await getLucia()
    await lucia.invalidateUserSessions(user.id)
    const session = await lucia.createSession(user.id, {})

    // Update last login
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLogin: new Date() },
    })

    const expiresAt = Date.now() + 30 * 24 * 60 * 60 * 1000 // 30 days

    return c.json({
      user: user,
      token: session.id,
      refreshToken: session.id,
      expiresAt: expiresAt,
    })
  } catch (error) {
    console.error('WebAuthn registration finish error:', error)
    return c.json({ error: 'Failed to complete registration' }, 500)
  }
})

// Authentication endpoints
app.post('/auth/webauthn/authenticate/begin', async c => {
  try {
    const body = (await c.req.json()) as { email: string }

    if (!body.email) {
      return c.json({ error: 'Email is required' }, 400)
    }

    // Find user and their credentials
    const user = await prisma.user.findUnique({
      where: { email: body.email },
      include: { WebAuthnCredential: true },
    })

    if (!user) {
      return c.json({ error: 'User not found' }, 404)
    }

    if (user.WebAuthnCredential.length === 0) {
      return c.json({ error: 'No WebAuthn credentials found for this user' }, 400)
    }

    const userCredentials = user.WebAuthnCredential.map(cred => ({
      id: cred.credentialId,
      transports: JSON.parse(cred.transports) as AuthenticatorTransport[],
    }))

    const options = await generateAuthenticationOptions({
      rpID,
      allowCredentials: userCredentials,
      userVerification: 'preferred',
    })

    // Store challenge temporarily
    challenges.set(user.email, `${options.challenge}|${Date.now()}`)
    cleanupOldChallenges()

    return c.json(options)
  } catch (error) {
    console.error('WebAuthn authentication begin error:', error)
    return c.json({ error: 'Failed to generate authentication options' }, 500)
  }
})

app.post('/auth/webauthn/authenticate/finish', async c => {
  try {
    const body = (await c.req.json()) as {
      email: string
      authenticationResponse: AuthenticationResponseJSON
    }

    if (!body.email || !body.authenticationResponse) {
      return c.json({ error: 'Email and authentication response are required' }, 400)
    }

    // Get stored challenge
    const challengeData = challenges.get(body.email)
    if (!challengeData) {
      return c.json({ error: 'No challenge found for this email' }, 400)
    }

    const [expectedChallenge] = challengeData.split('|')

    // Find user and credential
    const user = await prisma.user.findUnique({
      where: { email: body.email },
      include: { WebAuthnCredential: true },
    })

    if (!user) {
      return c.json({ error: 'User not found' }, 404)
    }

    const credentialId = body.authenticationResponse.id
    const credential = user.WebAuthnCredential.find(cred => cred.credentialId === credentialId)

    if (!credential) {
      return c.json({ error: 'Credential not found' }, 404)
    }

    // Verify authentication response
    const verification = await verifyAuthenticationResponse({
      response: body.authenticationResponse,
      expectedChallenge,
      expectedOrigin: origin,
      expectedRPID: rpID,
      credential: {
        id: credential.credentialId,
        publicKey: credential.publicKey,
        counter: Number(credential.counter),
        transports: JSON.parse(credential.transports),
      },
    })

    if (!verification.verified) {
      return c.json({ error: 'Authentication verification failed' }, 400)
    }

    // Update credential counter
    await prisma.webAuthnCredential.update({
      where: { id: credential.id },
      data: { counter: BigInt(verification.authenticationInfo.newCounter) },
    })

    // Clean up challenge
    challenges.delete(body.email)

    // Create session
    const lucia = await getLucia()
    await lucia.invalidateUserSessions(user.id)
    const session = await lucia.createSession(user.id, {})

    // Update last login
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLogin: new Date() },
    })

    const expiresAt = Date.now() + 30 * 24 * 60 * 60 * 1000 // 30 days

    return c.json({
      user: user,
      token: session.id,
      refreshToken: session.id,
      expiresAt: expiresAt,
    })
  } catch (error) {
    console.error('WebAuthn authentication finish error:', error)
    return c.json({ error: 'Failed to complete authentication' }, 500)
  }
})
