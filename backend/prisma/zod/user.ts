import * as z from "zod"
import { CompleteSession, RelatedSessionModel, CompleteDevice, RelatedDeviceModel, CompletePushSubscription, RelatedPushSubscriptionModel, CompleteTemperature, RelatedTemperatureModel, CompletePushNotification, RelatedPushNotificationModel, CompleteStatisticValues, RelatedStatisticValuesModel, CompleteStatisticElectricity, RelatedStatisticElectricityModel, CompleteApiKey, RelatedApiKeyModel, CompleteNotification, RelatedNotificationModel, CompleteWebAuthnCredential, RelatedWebAuthnCredentialModel } from "./index"

export const UserModel = z.object({
  id: z.string(),
  email: z.string(),
  password: z.string(),
  name: z.string().nullish(),
  image: z.string().nullish(),
  isEmailVerified: z.boolean(),
  lastLogin: z.date().nullish(),
})

export interface CompleteUser extends z.infer<typeof UserModel> {
  sessions: CompleteSession[]
  Device: CompleteDevice[]
  PushSubscription: CompletePushSubscription[]
  Temperature: CompleteTemperature[]
  PushNotification: CompletePushNotification[]
  StatisticValues: CompleteStatisticValues[]
  StatisticElectricity: CompleteStatisticElectricity[]
  ApiKey: CompleteApiKey[]
  Notification: CompleteNotification[]
  WebAuthnCredential: CompleteWebAuthnCredential[]
}

/**
 * RelatedUserModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedUserModel: z.ZodSchema<CompleteUser> = z.lazy(() => UserModel.extend({
  sessions: RelatedSessionModel.array(),
  Device: RelatedDeviceModel.array(),
  PushSubscription: RelatedPushSubscriptionModel.array(),
  Temperature: RelatedTemperatureModel.array(),
  PushNotification: RelatedPushNotificationModel.array(),
  StatisticValues: RelatedStatisticValuesModel.array(),
  StatisticElectricity: RelatedStatisticElectricityModel.array(),
  ApiKey: RelatedApiKeyModel.array(),
  Notification: RelatedNotificationModel.array(),
  WebAuthnCredential: RelatedWebAuthnCredentialModel.array(),
}))
