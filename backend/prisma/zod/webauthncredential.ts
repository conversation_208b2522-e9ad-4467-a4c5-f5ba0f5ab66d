import * as z from "zod"
import { CompleteUser, RelatedUserModel } from "./index"

export const WebAuthnCredentialModel = z.object({
  id: z.string(),
  credentialId: z.string(),
  publicKey: z.unknown(),
  counter: z.bigint(),
  deviceType: z.string(),
  backedUp: z.boolean(),
  transports: z.string(),
  userId: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

export interface CompleteWebAuthnCredential extends z.infer<typeof WebAuthnCredentialModel> {
  user: CompleteUser
}

/**
 * RelatedWebAuthnCredentialModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedWebAuthnCredentialModel: z.ZodSchema<CompleteWebAuthnCredential> = z.lazy(() => WebAuthnCredentialModel.extend({
  user: RelatedUserModel,
}))
