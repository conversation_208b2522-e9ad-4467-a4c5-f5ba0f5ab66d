import { useEffect, useRef } from 'react'
import useAuthenticationStore from 'stores/AuthenticationStore'
import { isTokenExpired, getTokenExpiration } from 'helpers/tokenUtils'

/**
 * Hook that automatically manages token refresh
 * - Checks token expiration on mount
 * - Sets up periodic token refresh checks
 * - Handles token expiration detection from JWT payload
 */
export function useTokenRefresh() {
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const { isAuthenticated, token, tokenExpiresAt, refreshAuthToken, setTokens } =
    useAuthenticationStore()

  useEffect(() => {
    if (!isAuthenticated || !token) {
      // Clear any existing interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
      return
    }

    // If we don't have an expiration time stored, try to extract it from the token
    if (!tokenExpiresAt) {
      const expiration = getTokenExpiration(token)
      if (expiration) {
        setTokens(token, useAuthenticationStore.getState().refreshToken, expiration)
      }
    }

    // Check token immediately
    const checkAndRefreshToken = async () => {
      const currentState = useAuthenticationStore.getState()

      if (!currentState.isAuthenticated) {
        return
      }

      const currentExpiration =
        currentState.tokenExpiresAt || getTokenExpiration(currentState.token)

      if (isTokenExpired(currentExpiration, 10)) {
        // 10 minutes buffer
        console.log('Token is expired or will expire soon, attempting refresh...')

        const success = await refreshAuthToken()
        if (!success) {
          console.error('Token refresh failed, user will be logged out')
        }
      }
    }

    // Check immediately
    checkAndRefreshToken()

    // Set up periodic checks every 5 minutes
    intervalRef.current = setInterval(checkAndRefreshToken, 5 * 60 * 1000)

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [isAuthenticated, token, tokenExpiresAt, refreshAuthToken, setTokens])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [])
}
