import useIntegrationStore from 'stores/IntegrationStore'
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { post } from 'helpers/http'

export interface User {
  id: string
  name: string
  email: string
  imageUrl?: string
}

interface AuthenticationStates {
  isAuthenticated: boolean
  user?: User
  token: string
  refreshToken?: string
  tokenExpiresAt?: number
  isRefreshing: boolean
  login: (user: User, token: string, refreshToken?: string, expiresAt?: number) => void
  logout: () => void
  refreshAuthToken: () => Promise<boolean>
  setTokens: (token: string, refreshToken?: string, expiresAt?: number) => void
}

const useAuthenticationStore = create<AuthenticationStates>()(
  persist(
    (set, get) => ({
      isAuthenticated: false,
      user: undefined,
      token: '',
      refreshToken: undefined,
      tokenExpiresAt: undefined,
      isRefreshing: false,
      login: (user: User, token: string, refreshToken?: string, expiresAt?: number) => {
        set({
          isAuthenticated: true,
          user,
          token,
          refreshToken,
          tokenExpiresAt: expiresAt,
        })
        useIntegrationStore.getState().loadIntregration()
      },
      logout: () => {
        set({
          isAuthenticated: false,
          user: undefined,
          token: '',
          refreshToken: undefined,
          tokenExpiresAt: undefined,
          isRefreshing: false,
        })
        useIntegrationStore.getState().clear()
      },
      setTokens: (token: string, refreshToken?: string, expiresAt?: number) => {
        set({ token, refreshToken, tokenExpiresAt: expiresAt })
      },
      refreshAuthToken: async (): Promise<boolean> => {
        const state = get()

        if (state.isRefreshing) {
          return false
        }

        if (!state.refreshToken) {
          console.warn('No refresh token available')
          return false
        }

        set({ isRefreshing: true })

        try {
          const response = await post('auth/refresh', {
            refreshToken: state.refreshToken,
          })

          const data = (await response.json()) as {
            token: string
            refreshToken?: string
            expiresAt?: number
          }

          set({
            token: data.token,
            refreshToken: data.refreshToken || state.refreshToken,
            tokenExpiresAt: data.expiresAt,
            isRefreshing: false,
          })

          return true
        } catch (error) {
          console.error('Token refresh failed:', error)
          set({ isRefreshing: false })

          // If refresh fails, logout the user
          get().logout()
          return false
        }
      },
    }),
    {
      name: 'philun-authentication',
      // storage: createJSONStorage(),
    }
  )
)

export default useAuthenticationStore
