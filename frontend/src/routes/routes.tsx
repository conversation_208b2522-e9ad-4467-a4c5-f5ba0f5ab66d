import BrewingViewPage from 'page/BrewingViewPage'
import BrewLogPage from 'page/BrewLogPage'
import DashboardPage from 'page/DashboardPage'
import DevicePage from 'page/DevicePage'
import DeviceView from 'page/Fermentation/DeviceView'
import FermentationDashboard from 'page/Fermentation/FermentationDashboard'
import ForgotPasswordPage from 'page/ForgotPasswordPage'
import InventoryPage from 'page/InventoryPage'
import LoginPage from 'page/LoginPage'
import RecipeBraurekaViewPage from 'page/RecipeBraurekaViewPage'
import RecipePage from 'page/RecipePage'
import RecipeViewPage from 'page/RecipeViewPage'
import RegisterPage from 'page/RegisterPage'
import UserSettingsPage from 'page/UserSettingsPage'
import { Navigate, Outlet, RouteObject } from 'react-router-dom'

import AppMenu from 'components/AppMenu'
import AuthenticationGuard from 'components/AuthenticationGuard'
import DevPage from 'page/DevPage'

const AuthenticationRoutes: RouteObject[] = [
  {
    path: '/login',
    element: <LoginPage />,
  },
  {
    path: '/register',
    element: <RegisterPage />,
  },
  {
    path: '/forgot-password',
    element: <ForgotPasswordPage />,
  },
]

const UserAppRoutes: RouteObject = {
  path: '/',
  element: (
    <AuthenticationGuard>
      <AppMenu />
    </AuthenticationGuard>
  ),
  //   errorElement: <ErrorPage />,
  children: [
    {
      index: true,
      element: <Navigate to="overview" replace={true} />,
    },
    {
      path: 'overview',
      element: <DashboardPage />,
    },
    {
      path: 'fermentation',
      element: <FermentationDashboard />,
    },
    {
      path: 'inventory',
      element: <InventoryPage />,
    },
    {
      path: 'recipe',
      element: <RecipePage />,
    },
    {
      path: 'recipe/:id/view',
      element: <RecipeViewPage />,
    },
    {
      path: 'recipe/braureka/:id/view',
      element: <RecipeBraurekaViewPage />,
    },
    {
      path: 'device',
      element: <DevicePage />,
    },
    {
      path: 'user/settings',
      element: <UserSettingsPage />,
    },
    {
      path: 'brewlog',
      element: <BrewLogPage />,
    },
    {
      path: 'brewing/:id',
      element: <BrewingViewPage />,
    },
    {
      path: 'fermentation/device/:deviceId',
      element: <DeviceView />,
    },
  ],
}

if (import.meta.env.DEV) {
  UserAppRoutes.children?.push({
    path: 'dev',
    element: <DevPage />,
  })
}

export const AppRoutes: RouteObject = {
  path: '/',
  element: <Outlet />,
  // errorElement: <ErrorPage />,
  children: [...AuthenticationRoutes, UserAppRoutes],
}
