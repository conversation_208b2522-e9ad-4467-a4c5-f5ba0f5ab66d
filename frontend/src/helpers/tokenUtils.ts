import useAuthenticationStore from 'stores/AuthenticationStore'

/**
 * Checks if a token is expired or will expire soon
 * @param expiresAt - Token expiration timestamp in milliseconds
 * @param bufferMinutes - Minutes before expiration to consider token as expired (default: 5)
 * @returns true if token is expired or will expire soon
 */
export function isTokenExpired(expiresAt?: number, bufferMinutes: number = 5): boolean {
  if (!expiresAt) {
    return false // If no expiration time, assume token is valid
  }
  
  const now = Date.now()
  const bufferMs = bufferMinutes * 60 * 1000
  
  return now >= (expiresAt - bufferMs)
}

/**
 * Attempts to refresh the authentication token if it's expired or will expire soon
 * @returns Promise<boolean> - true if token was refreshed or is still valid, false if refresh failed
 */
export async function ensureValidToken(): Promise<boolean> {
  const store = useAuthenticationStore.getState()
  
  if (!store.isAuthenticated) {
    return false
  }
  
  // If token is not expired, return true
  if (!isTokenExpired(store.tokenExpiresAt)) {
    return true
  }
  
  // If already refreshing, wait for it to complete
  if (store.isRefreshing) {
    // Wait for refresh to complete (with timeout)
    let attempts = 0
    const maxAttempts = 50 // 5 seconds max wait
    
    while (store.isRefreshing && attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 100))
      attempts++
    }
    
    // Check if refresh was successful
    return !isTokenExpired(useAuthenticationStore.getState().tokenExpiresAt)
  }
  
  // Attempt to refresh the token
  return await store.refreshAuthToken()
}

/**
 * Decodes a JWT token payload (without verification)
 * Note: This is for client-side token inspection only, not for security validation
 * @param token - JWT token string
 * @returns Decoded payload or null if invalid
 */
export function decodeJWTPayload(token: string): any | null {
  try {
    const parts = token.split('.')
    if (parts.length !== 3) {
      return null
    }
    
    const payload = parts[1]
    const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'))
    return JSON.parse(decoded)
  } catch (error) {
    console.error('Failed to decode JWT payload:', error)
    return null
  }
}

/**
 * Extracts expiration time from a JWT token
 * @param token - JWT token string
 * @returns Expiration timestamp in milliseconds or undefined if not found
 */
export function getTokenExpiration(token: string): number | undefined {
  const payload = decodeJWTPayload(token)
  if (!payload || !payload.exp) {
    return undefined
  }
  
  // JWT exp is in seconds, convert to milliseconds
  return payload.exp * 1000
}
