import { XCircleIcon } from '@heroicons/react/20/solid'
import { zodResolver } from '@hookform/resolvers/zod'
import { iconUrl } from 'constants'
import { useState } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { Link, useNavigate } from 'react-router-dom'
import useAuthenticationStore, { User } from 'stores/AuthenticationStore'
import { z } from 'zod'

import AppleIcon from 'assets/apple.svg?react'

import { post, RequestError } from 'helpers/http'
import useStatus from 'hooks/useStatus'

import Checkbox from 'components/Common/Form/Checkbox'
import TextInput from 'components/Common/Form/TextInput'
import WebAuthnButton from 'components/Authentication/WebAuthnButton'

interface LoginForm {
  email: string
  password: string
  rememberMe: boolean
}

const STORAGE_LOGIN_LABEL = 'philun-login'

export default function LoginPage() {
  const navigate = useNavigate()
  const { t } = useTranslation()

  const StatusLabel = useStatus()

  const [error, setError] = useState<string | null>(null)

  const onLogin = async (data: LoginForm) => {
    setError(null)

    try {
      if (data.rememberMe) {
        localStorage.setItem(STORAGE_LOGIN_LABEL, JSON.stringify(data))
      }

      const request = await post('login', data)

      const response = (await request.json()) as {
        user: User
        token: string
        refreshToken?: string
        expiresAt?: number
      }

      useAuthenticationStore
        .getState()
        .login(response.user, response.token, response.refreshToken, response.expiresAt)
      navigate('/')
    } catch (error) {
      if (error instanceof RequestError) {
        if (error.response.status === 401) {
          setError(t('authentication.emailOrPasswordWrong'))
        } else if (error.response.status !== 200) {
          setError(t('common.errorOccured'))
        }
      } else {
        console.error('Error while logging in:', error)
      }
    }
  }

  const rememberMeDate = JSON.parse(localStorage.getItem(STORAGE_LOGIN_LABEL) || '{}') as LoginForm
  const methods = useForm<LoginForm>({
    resolver: zodResolver(
      z.object({
        email: z.string().email(),
        password: z.string().min(8),
        rememberMe: z.boolean().optional(),
      })
    ),
    defaultValues: rememberMeDate,
  })

  const email = methods.watch('email')

  return (
    <>
      <div className="flex min-h-full flex-1 flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <img alt={t('appname')} src={iconUrl} className="mx-auto h-10 w-auto" />
          <h2 className="mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900">
            {t('authentication.signInToYourAccount')}
          </h2>
        </div>

        <div className="mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]">
          <div className="bg-white px-6 py-12 shadow sm:rounded-lg sm:px-12">
            <FormProvider {...methods}>
              <form
                onSubmit={methods.handleSubmit(data => {
                  onLogin(data)
                })}
                className="space-y-6"
              >
                {error && (
                  <div className="rounded-md bg-red-50 p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <XCircleIcon aria-hidden="true" className="h-5 w-5 text-red-400" />
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">{error}</h3>
                      </div>
                    </div>
                  </div>
                )}

                <TextInput name="email" label={t('authentication.email')} />

                <TextInput name="password" label={t('authentication.password')} type="password" />

                <div className="flex items-center justify-between">
                  <Checkbox name="rememberMe" label={t('authentication.rememberMe')} />

                  <div className="text-sm leading-6">
                    <Link
                      to={'/forgot-password?email=' + email}
                      className="font-semibold text-indigo-600 hover:text-indigo-500"
                    >
                      {t('authentication.forgotPassword')}
                    </Link>
                  </div>
                </div>

                <div>
                  <button
                    type="submit"
                    className="flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                  >
                    {t('authentication.login')}
                  </button>
                </div>
              </form>
            </FormProvider>

            <div>
              <div className="relative mt-10">
                <div aria-hidden="true" className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-200" />
                </div>
                <div className="relative flex justify-center text-sm font-medium leading-6">
                  <span className="bg-white px-6 text-gray-900">
                    {t('authentication.orContinueWith')}
                  </span>
                </div>
              </div>

              <div className="mt-6 grid grid-cols-2 gap-4">
                <a
                  href="#"
                  className="flex w-full items-center justify-center gap-3 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus-visible:ring-transparent"
                >
                  <svg viewBox="0 0 24 24" aria-hidden="true" className="h-5 w-5">
                    <path
                      d="M12.0003 4.75C13.7703 4.75 15.3553 5.36002 16.6053 6.54998L20.0303 3.125C17.9502 1.19 15.2353 0 12.0003 0C7.31028 0 3.25527 2.69 1.28027 6.60998L5.27028 9.70498C6.21525 6.86002 8.87028 4.75 12.0003 4.75Z"
                      fill="#EA4335"
                    />
                    <path
                      d="M23.49 12.275C23.49 11.49 23.415 10.73 23.3 10H12V14.51H18.47C18.18 15.99 17.34 17.25 16.08 18.1L19.945 21.1C22.2 19.01 23.49 15.92 23.49 12.275Z"
                      fill="#4285F4"
                    />
                    <path
                      d="M5.26498 14.2949C5.02498 13.5699 4.88501 12.7999 4.88501 11.9999C4.88501 11.1999 5.01998 10.4299 5.26498 9.7049L1.275 6.60986C0.46 8.22986 0 10.0599 0 11.9999C0 13.9399 0.46 15.7699 1.28 17.3899L5.26498 14.2949Z"
                      fill="#FBBC05"
                    />
                    <path
                      d="M12.0004 24.0001C15.2404 24.0001 17.9654 22.935 19.9454 21.095L16.0804 18.095C15.0054 18.82 13.6204 19.245 12.0004 19.245C8.8704 19.245 6.21537 17.135 5.2654 14.29L1.27539 17.385C3.25539 21.31 7.3104 24.0001 12.0004 24.0001Z"
                      fill="#34A853"
                    />
                  </svg>
                  <span className="text-sm font-semibold leading-6">Google</span>
                </a>

                <a
                  href="#"
                  className="flex w-full items-center justify-center gap-3 rounded-md bg-black px-3 py-2 text-sm font-semibold text-white shadow-sm ring-1 ring-inset ring-gray-900 focus-visible:ring-transparent"
                >
                  <AppleIcon />
                  <span className="text-sm font-semibold leading-6">Sign in with Apple</span>
                </a>
              </div>
            </div>

            <WebAuthnButton mode="login" email={email} onError={setError} />
          </div>

          <p className="mt-10 text-center text-sm text-gray-500">
            {t('authentication.notAMember') + ' '}
            <Link
              to="/register"
              className="font-semibold leading-6 text-indigo-600 hover:text-indigo-500"
            >
              {t('authentication.register')}
            </Link>
            {/* Start a 14 day free trial */}
          </p>

          <StatusLabel />
        </div>
      </div>
    </>
  )
}
