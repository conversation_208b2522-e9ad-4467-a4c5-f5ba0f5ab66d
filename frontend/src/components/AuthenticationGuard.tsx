import { ReactElement, useEffect } from 'react'
import { Navigate } from 'react-router-dom'
import useAuthenticationStore from 'stores/AuthenticationStore'
import useIntegrationStore from 'stores/IntegrationStore'
import { useTokenRefresh } from 'hooks/useTokenRefresh'

export default function AuthenticationGuard({ children }: { children: ReactElement }) {
  const isAuthenticated = useAuthenticationStore(state => state.isAuthenticated)

  // Initialize token refresh management
  useTokenRefresh()

  useEffect(() => {
    if (isAuthenticated) {
      useIntegrationStore.getState().loadIntregration()
    }
  }, [isAuthenticated])

  if (!isAuthenticated) {
    return <Navigate to="/login" replace={true} />
  }

  return <>{children}</>
}
