import type { Device } from '@prisma/client'
import { differenceInMinutes, format, isAfter, parseISO } from 'date-fns'
import { BarChartIcon, LineChartIcon, TableIcon } from 'lucide-react'
import { useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts'

import { formatTemperature } from 'helpers/format'
import { useGetSpindelStatistics } from 'services/Device/SpindelService'
import { useTemperatureStatistic } from 'services/FermentationService'

type ViewMode = 'graph' | 'table'

type DataEntry = {
  id: number
  createdAt: string | Date
  temperature?: number
  gravity?: number
  [key: string]: string | number | Date | undefined
}

// Create a common type for chart data
type ChartDataPoint = {
  time: string | Date
  timeFormatted: string
  temperature?: number
  plato?: number
  formattedTime?: string // For display in the chart
}

// Helper function to format a date consistently
const formatDate = (date: string | Date): Date => {
  return typeof date === 'string' ? parseISO(date) : date
}

// Helper function to interpolate missing values between data points
const interpolateMissingValues = (
  dataPoints: DataEntry[],
  valueKey: 'temperature' | 'gravity'
): DataEntry[] => {
  if (!dataPoints || dataPoints.length < 2) return dataPoints

  const result: DataEntry[] = []

  // Sort by creation time
  const sortedData = [...dataPoints].sort((a, b) => {
    const aTime = formatDate(a.createdAt).getTime()
    const bTime = formatDate(b.createdAt).getTime()
    return aTime - bTime
  })

  // Add the first point
  result.push(sortedData[0])

  // Process consecutive pairs
  for (let i = 0; i < sortedData.length - 1; i++) {
    const current = sortedData[i]
    const next = sortedData[i + 1]

    // Skip if either point is missing the value we're interpolating
    if (current[valueKey] === undefined || next[valueKey] === undefined) {
      if (i < sortedData.length - 2) continue
      result.push(next)
      continue
    }

    const currentTime = formatDate(current.createdAt).getTime()
    const nextTime = formatDate(next.createdAt).getTime()
    const timeDiff = nextTime - currentTime

    // Only interpolate if time difference is significant (more than 10 minutes)
    if (timeDiff > 10 * 60 * 1000) {
      // Create two interpolation points
      const oneThirdTime = new Date(currentTime + timeDiff / 3)
      const twoThirdsTime = new Date(currentTime + (timeDiff * 2) / 3)

      // Linear interpolation for the value
      const currentValue = current[valueKey] as number
      const nextValue = next[valueKey] as number
      const valueDiff = nextValue - currentValue

      const oneThirdValue = currentValue + valueDiff / 3
      const twoThirdsValue = currentValue + (valueDiff * 2) / 3

      // Add interpolated points
      result.push({
        id: -1 * (result.length + 1), // Use negative IDs for interpolated points
        createdAt: oneThirdTime,
        [valueKey]: oneThirdValue,
        isInterpolated: true,
      } as unknown as DataEntry)

      result.push({
        id: -1 * (result.length + 1),
        createdAt: twoThirdsTime,
        [valueKey]: twoThirdsValue,
        isInterpolated: true,
      } as unknown as DataEntry)
    }

    // Add the next point
    if (i === sortedData.length - 2) {
      result.push(next)
    }
  }

  return result
}

// Helper function to filter and accumulate data points based on time constraints
const filterAndAccumulateData = (
  data: DataEntry[],
  deviceCreatedAt: string | Date,
  maxPoints = 50,
  minMinutesBetweenPoints = 5
) => {
  if (!data || data.length === 0) return []

  // Convert deviceCreatedAt to Date if it's a string
  const deviceCreationDate = formatDate(deviceCreatedAt)

  // Filter data to only include points after device creation
  const filteredData = data.filter(entry => {
    const entryDate = formatDate(entry.createdAt)
    return isAfter(entryDate, deviceCreationDate)
  })

  if (filteredData.length <= maxPoints) {
    // If we have fewer points than the max, just make sure they're at least minMinutesBetweenPoints apart
    const result = []
    let lastAddedEntryTime: Date | null = null

    for (const entry of filteredData) {
      const currentEntryTime = formatDate(entry.createdAt)

      if (
        !lastAddedEntryTime ||
        differenceInMinutes(currentEntryTime, lastAddedEntryTime) >= minMinutesBetweenPoints
      ) {
        result.push(entry)
        lastAddedEntryTime = currentEntryTime
      }
    }

    return result
  }

  // If we have more than maxPoints, we need to distribute them evenly across the time range
  const sortedData = [...filteredData].sort((a, b) => {
    const aTime = formatDate(a.createdAt)
    const bTime = formatDate(b.createdAt)
    return aTime.getTime() - bTime.getTime()
  })

  const firstDate = formatDate(sortedData[0].createdAt)
  const lastDate = formatDate(sortedData[sortedData.length - 1].createdAt)
  const totalMinutes = differenceInMinutes(lastDate, firstDate)

  // Calculate the ideal time gap between points
  const minutesPerPoint = Math.max(
    minMinutesBetweenPoints,
    Math.floor(totalMinutes / (maxPoints - 1))
  )

  const result = [sortedData[0]] // Always include the first point
  let currentTargetTime = new Date(firstDate.getTime() + minutesPerPoint * 60 * 1000)

  for (let i = 1; i < sortedData.length - 1; i++) {
    const entryTime = formatDate(sortedData[i].createdAt)

    if (
      isAfter(entryTime, currentTargetTime) ||
      entryTime.getTime() === currentTargetTime.getTime()
    ) {
      result.push(sortedData[i])
      currentTargetTime = new Date(entryTime.getTime() + minutesPerPoint * 60 * 1000)

      // Break if we've reached our max points (minus 1 to save room for the last point)
      if (result.length >= maxPoints - 1) break
    }
  }

  // Always include the last point if we haven't reached max
  if (
    result.length < maxPoints &&
    result[result.length - 1] !== sortedData[sortedData.length - 1]
  ) {
    result.push(sortedData[sortedData.length - 1])
  }

  return result
}

// Helper to merge temperature and spindle data into a single timeline
const mergeDataSets = (
  temperatureData: DataEntry[],
  spindleData: DataEntry[]
): ChartDataPoint[] => {
  if (!temperatureData.length && !spindleData.length) return []
  if (!temperatureData.length) {
    // Only spindle data available
    return spindleData.map(entry => ({
      time: entry.createdAt,
      timeFormatted: format(formatDate(entry.createdAt), 'HH:mm'),
      plato: entry.gravity,
    }))
  }
  if (!spindleData.length) {
    // Only temperature data available
    return temperatureData.map(entry => ({
      time: entry.createdAt,
      timeFormatted: format(formatDate(entry.createdAt), 'HH:mm'),
      temperature: entry.temperature,
    }))
  }

  // Both datasets available, merge them by time
  const allData = [...temperatureData, ...spindleData].sort((a, b) => {
    const aTime = formatDate(a.createdAt).getTime()
    const bTime = formatDate(b.createdAt).getTime()
    return aTime - bTime
  })

  // Create a map to collect data by time buckets (in minutes)
  const dataMap: Record<string, ChartDataPoint> = {}

  // Process all entries chronologically
  allData.forEach(entry => {
    const entryDate = formatDate(entry.createdAt)
    const timeKey = entryDate.getTime().toString()

    if (!dataMap[timeKey]) {
      // Initialize a new data point
      dataMap[timeKey] = {
        time: entry.createdAt,
        timeFormatted: format(entryDate, 'HH:mm'),
        formattedTime: format(entryDate, 'yyyy-MM-dd HH:mm'),
      }
    }

    // Add the temperature or plato value to the data point
    if (entry.temperature !== undefined) {
      dataMap[timeKey].temperature = entry.temperature
    }
    if (entry.gravity !== undefined) {
      dataMap[timeKey].plato = entry.gravity
    }
  })

  // Convert the map back to an array and sort by time
  const mergedData = Object.values(dataMap).sort((a, b) => {
    const aTime = formatDate(a.time).getTime()
    const bTime = formatDate(b.time).getTime()
    return aTime - bTime
  })

  // Apply filtering to limit points
  if (mergedData.length > 50) {
    const step = Math.ceil(mergedData.length / 50)
    return mergedData.filter((_, index) => index % step === 0 || index === mergedData.length - 1)
  }

  return mergedData
}

const StatisticsSection = ({ device, spindle }: { device: Device; spindle?: Device }) => {
  const { t } = useTranslation()
  const [viewMode, setViewMode] = useState<ViewMode>('graph')

  const { data, isLoading, isError } = useTemperatureStatistic(device.id)
  const { data: spindleData } = useGetSpindelStatistics(spindle?.id ?? 0)

  // Format data for the chart
  const chartData = useMemo(() => {
    // Filter temperature data based on device creation time
    const processedTempData = filterAndAccumulateData(data ?? [], device.createdAt, 50, 5)

    // Filter spindle data based on spindle creation time (if available)
    const processedSpindleData = spindle
      ? filterAndAccumulateData(spindleData ?? [], spindle.createdAt, 50, 5)
      : []

    // Interpolate missing values in spindle data
    const interpolatedSpindleData = interpolateMissingValues(processedSpindleData, 'gravity')

    // Merge the two datasets into a single timeline with both temperature and plato values
    return mergeDataSets(processedTempData, interpolatedSpindleData)
  }, [data, spindleData, device.createdAt, spindle?.createdAt])

  return (
    <div className="mt-4 border-t pt-4">
      <div className="mb-3 flex items-center justify-between">
        <div className="flex items-center">
          <BarChartIcon size={18} className="mr-2 text-gray-700" />
          <h3 className="font-medium">{t('fermentation.statistics')}</h3>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => setViewMode('graph')}
            className={`rounded-md px-3 py-1.5 text-xs font-medium ${
              viewMode === 'graph'
                ? 'bg-blue-100 text-blue-600'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
            aria-label="Show graph view"
          >
            <div className="flex items-center">
              <LineChartIcon size={14} className="mr-1" />
              {t('common.graph')}
            </div>
          </button>
          <button
            onClick={() => setViewMode('table')}
            className={`rounded-md px-3 py-1.5 text-xs font-medium ${
              viewMode === 'table'
                ? 'bg-blue-100 text-blue-600'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
            aria-label="Show table view"
          >
            <div className="flex items-center">
              <TableIcon size={14} className="mr-1" />
              {t('common.table')}
            </div>
          </button>
        </div>
      </div>
      <div className="rounded-lg bg-gray-50 p-4">
        {isLoading && <div className="mt-2 text-sm text-gray-500">{t('common.loading')}</div>}
        {isError && <div className="mt-2 text-sm text-red-500">{t('common.error')}</div>}
        {data && (
          <>
            {viewMode === 'graph' && (
              <div className="mb-6 h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="timeFormatted"
                      label={{
                        value: t('fermentation.time'),
                        position: 'insideBottomRight',
                        offset: 0,
                      }}
                    />
                    <YAxis
                      yAxisId="temperature"
                      label={{ value: t('common.temperature'), angle: -90, position: 'insideLeft' }}
                    />
                    {spindle && (
                      <YAxis
                        yAxisId="plato"
                        orientation="right"
                        label={{
                          value: t('fermentation.plato'),
                          angle: 90,
                          position: 'insideRight',
                        }}
                      />
                    )}
                    <Tooltip
                      formatter={(value, name) => {
                        if (name === 'temperature') return formatTemperature(value as number)
                        if (name === 'plato') return `${value}°P`
                        return value
                      }}
                      labelFormatter={label => t('fermentation.time') + ': ' + label}
                    />
                    <Legend />
                    <Line
                      yAxisId="temperature"
                      type="monotone"
                      dataKey="temperature"
                      name={t('common.temperature')}
                      stroke="#8884d8"
                      activeDot={{ r: 8 }}
                    />
                    {spindle && (
                      <Line
                        yAxisId="plato"
                        type="monotone"
                        dataKey="plato"
                        name={t('fermentation.plato')}
                        stroke="#82ca9d"
                      />
                    )}
                  </LineChart>
                </ResponsiveContainer>
              </div>
            )}
            {viewMode === 'table' && (
              <div
                className={
                  'grid gap-2 overflow-x-auto ' + (spindle ? 'sm:grid-cgrid-cols-2' : 'grid-cols-1')
                }
              >
                <table className="min-w-full table-auto divide-y divide-gray-200">
                  <thead>
                    <tr>
                      <th className="px-3 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        {t('fermentation.time')}
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        {t('common.temperature')}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 bg-white">
                    {data.slice(0, 100).map(entry => (
                      <tr key={entry.id}>
                        <td className="px-3 py-2 text-sm text-gray-800">
                          {format(
                            typeof entry.createdAt === 'string'
                              ? parseISO(entry.createdAt)
                              : entry.createdAt,
                            'yyyy-MM-dd HH:mm:ss'
                          )}
                        </td>
                        <td className="px-3 py-2 text-sm text-gray-800">
                          {formatTemperature(entry.temperature)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {spindle && (
                  <div>
                    <table className="min-w-full table-auto divide-y divide-gray-200">
                      <thead>
                        <tr>
                          <th className="px-3 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                            {t('fermentation.time')}
                          </th>
                          <th className="px-3 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                            {t('fermentation.plato')}
                          </th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200 bg-white">
                        {spindleData?.length === 0 && (
                          <tr>
                            <td colSpan={2} className="p-3 text-gray-800">
                              {t('fermentation.noSpindleData')}
                            </td>
                          </tr>
                        )}
                        {spindleData?.slice(0, 100)?.map(entry => (
                          <tr key={entry.id}>
                            <td className="px-3 py-2 text-sm text-gray-800">
                              {format(
                                typeof entry.createdAt === 'string'
                                  ? parseISO(entry.createdAt)
                                  : entry.createdAt,
                                'yyyy-MM-dd HH:mm:ss'
                              )}
                            </td>
                            <td className="px-3 py-2 text-sm text-gray-800">{entry.gravity}°P</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}
export default StatisticsSection
