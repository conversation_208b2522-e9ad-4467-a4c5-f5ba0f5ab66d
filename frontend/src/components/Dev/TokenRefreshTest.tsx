import { useState } from 'react'
import useAuthenticationStore from 'stores/AuthenticationStore'
import { isTokenExpired } from 'helpers/tokenUtils'

/**
 * Hook that provides token refresh functionality for manual use
 */
function useManualTokenRefresh() {
  const { refreshAuthToken, isRefreshing } = useAuthenticationStore()

  const refresh = async (): Promise<boolean> => {
    if (isRefreshing) {
      console.warn('Token refresh already in progress')
      return false
    }

    return await refreshAuthToken()
  }

  return {
    refresh,
    isRefreshing,
  }
}

/**
 * Test component for token refresh functionality
 * This component is for development/testing purposes only
 */
export default function TokenRefreshTest() {
  const [testResult, setTestResult] = useState<string>('')
  const { refresh, isRefreshing } = useManualTokenRefresh()
  const { isAuthenticated, token, tokenExpiresAt, refreshToken, user } = useAuthenticationStore()

  const handleManualRefresh = async () => {
    setTestResult('Starting token refresh...')

    try {
      const success = await refresh()
      if (success) {
        setTestResult('Token refresh successful!')
      } else {
        setTestResult('Token refresh failed!')
      }
    } catch (error) {
      setTestResult(`Token refresh error: ${error}`)
    }
  }

  const checkTokenStatus = () => {
    if (!tokenExpiresAt) {
      setTestResult('No expiration time available')
      return
    }

    const expired = isTokenExpired(tokenExpiresAt, 5) // 5 minute buffer
    const timeLeft = Math.max(0, tokenExpiresAt - Date.now())
    const minutesLeft = Math.floor(timeLeft / (1000 * 60))

    setTestResult(`Token ${expired ? 'is expired' : 'is valid'}. Time left: ${minutesLeft} minutes`)
  }

  const simulateExpiredToken = () => {
    // Set token expiration to 1 minute ago for testing
    const expiredTime = Date.now() - 60 * 1000
    useAuthenticationStore.getState().setTokens(token, refreshToken, expiredTime)
    setTestResult('Token expiration set to 1 minute ago for testing')
  }

  if (!isAuthenticated) {
    return (
      <div className="rounded-lg bg-gray-100 p-4">
        <h3 className="mb-2 text-lg font-semibold">Token Refresh Test</h3>
        <p>Please log in to test token refresh functionality.</p>
      </div>
    )
  }

  return (
    <div className="rounded-lg bg-gray-100 p-4">
      <h3 className="mb-4 text-lg font-semibold">Token Refresh Test</h3>

      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <strong>User:</strong> {user?.email}
          </div>
          <div>
            <strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}
          </div>
          <div>
            <strong>Token:</strong> {token ? `${token.substring(0, 20)}...` : 'None'}
          </div>
          <div>
            <strong>Refresh Token:</strong>{' '}
            {refreshToken ? `${refreshToken.substring(0, 20)}...` : 'None'}
          </div>
          <div>
            <strong>Expires At:</strong>{' '}
            {tokenExpiresAt ? new Date(tokenExpiresAt).toLocaleString() : 'Unknown'}
          </div>
          <div>
            <strong>Is Refreshing:</strong> {isRefreshing ? 'Yes' : 'No'}
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          <button
            onClick={handleManualRefresh}
            disabled={isRefreshing}
            className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600 disabled:opacity-50"
          >
            {isRefreshing ? 'Refreshing...' : 'Manual Refresh'}
          </button>

          <button
            onClick={checkTokenStatus}
            className="rounded bg-green-500 px-4 py-2 text-white hover:bg-green-600"
          >
            Check Token Status
          </button>

          <button
            onClick={simulateExpiredToken}
            className="rounded bg-orange-500 px-4 py-2 text-white hover:bg-orange-600"
          >
            Simulate Expired Token
          </button>
        </div>

        {testResult && (
          <div className="rounded border bg-white p-3">
            <strong>Result:</strong> {testResult}
          </div>
        )}
      </div>
    </div>
  )
}
