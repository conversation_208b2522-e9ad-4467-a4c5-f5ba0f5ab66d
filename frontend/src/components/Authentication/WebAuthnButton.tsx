import { FingerPrintIcon } from '@heroicons/react/24/outline'
import { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import useAuthenticationStore from 'stores/AuthenticationStore'

import {
  isWebAuthnSupported,
  isPlatformAuthenticatorAvailable,
  registerWebAuthnCredential,
  authenticateWebAuthnCredential,
} from './WebAuthn'

interface WebAuthnButtonProps {
  mode: 'login' | 'register'
  email?: string
  onError?: (error: string) => void
}

export default function WebAuthnButton({ mode, email, onError }: WebAuthnButtonProps) {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const [isSupported, setIsSupported] = useState(false)
  const [isPlatformAvailable, setIsPlatformAvailable] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [showEmailInput, setShowEmailInput] = useState(false)
  const [emailInput, setEmailInput] = useState(email || '')

  useEffect(() => {
    checkWebAuthnSupport()
  }, [])

  const checkWebAuthnSupport = async () => {
    const supported = isWebAuthnSupported()
    const platformAvailable = await isPlatformAuthenticatorAvailable()

    setIsSupported(supported)
    setIsPlatformAvailable(platformAvailable)
  }

  const handleWebAuthn = async () => {
    if (!emailInput.trim()) {
      setShowEmailInput(true)
      return
    }

    setIsLoading(true)

    try {
      let result

      if (mode === 'register') {
        result = await registerWebAuthnCredential(emailInput.trim())
      } else {
        result = await authenticateWebAuthnCredential(emailInput.trim())
      }

      // Login successful
      useAuthenticationStore
        .getState()
        .login(result.user, result.token, result.refreshToken, result.expiresAt)
      navigate('/')
    } catch (error) {
      console.error('WebAuthn error:', error)
      const errorMessage =
        error instanceof Error ? error.message : t('authentication.webauthnError')

      if (onError) {
        onError(errorMessage)
      } else {
        // Show error in UI - you might want to use a toast or alert system
        alert(errorMessage)
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (emailInput.trim()) {
      setShowEmailInput(false)
      handleWebAuthn()
    }
  }

  if (!isSupported) {
    return null
  }

  return (
    <div className="mt-6">
      <div className="relative">
        <div className="absolute inset-0 flex items-center" aria-hidden="true">
          <div className="w-full border-t border-gray-200" />
        </div>
        <div className="relative flex justify-center text-sm font-medium leading-6">
          <span className="bg-white px-6 text-gray-900">{t('authentication.orUsePasskey')}</span>
        </div>
      </div>

      <div className="mt-6">
        {showEmailInput ? (
          <form onSubmit={handleEmailSubmit} className="space-y-4">
            <div>
              <label
                htmlFor="webauthn-email"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                {t('authentication.email')}
              </label>
              <div className="mt-2">
                <input
                  id="webauthn-email"
                  name="email"
                  type="email"
                  required
                  autoComplete="email"
                  value={emailInput}
                  onChange={e => setEmailInput(e.target.value)}
                  className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                  placeholder={t('authentication.emailPlaceholder')}
                />
              </div>
            </div>
            <div className="flex gap-3">
              <button
                type="button"
                onClick={() => setShowEmailInput(false)}
                className="flex-1 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
              >
                {t('common.cancel')}
              </button>
              <button
                type="submit"
                disabled={isLoading || !emailInput.trim()}
                className="flex-1 rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:opacity-50"
              >
                {isLoading ? t('authentication.authenticating') : t('common.continue')}
              </button>
            </div>
          </form>
        ) : (
          <button
            type="button"
            onClick={handleWebAuthn}
            disabled={isLoading}
            className="flex w-full items-center justify-center gap-3 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:opacity-50"
          >
            <FingerPrintIcon className="h-5 w-5" aria-hidden="true" />
            <span>
              {isLoading
                ? t('authentication.authenticating')
                : mode === 'register'
                  ? t('authentication.signUpWithPasskey')
                  : t('authentication.signInWithPasskey')}
            </span>
          </button>
        )}
      </div>

      {isPlatformAvailable && (
        <p className="mt-2 text-center text-xs text-gray-500">
          {t('authentication.passkeyDescription')}
        </p>
      )}
    </div>
  )
}
