
#include <string.h>
#include <Http.hpp>

static int _MaxMinusTemperature = -274;

long loadTimeFromApi() {
    std::string route = "api/v1/brewtool/time";
    std::string responseTime = getData(route);
    if (!responseTime.empty()) {
        Serial.println("Response time: " + String(responseTime.c_str()));   
        long timestamp = String(responseTime.c_str()).toInt();
        Serial.println("Timestamp: " + String(timestamp));
        return timestamp;
    }
    Serial.println("Response time is empty");
    return 0;
}

int getDestinatedTemperature() {
    Serial.println("Get destined temperature for mac: " + String(getMac().c_str())); 
    std::string route = "api/v1/brewtool/" + getMac() + "/config";
    std::string response = getData(route);
    Serial.println("Response: " + String(response.c_str()));
    if (response == HTTP_NOT_FOUND || response.empty()) {
        return _MaxMinusTemperature;
    } else if (!response.empty()) {
        size_t pos = response.find(';');
        std::string strTemperature = response.substr(0, pos);
        int temperature = String(strTemperature.c_str()).toInt();
        return temperature;
    } else {
        return _MaxMinusTemperature;
    }
}

/*
 Meldet die Schaltung der Kühlung oder Heizung an den Server
*/
void sendElectricalData(const char* type, bool status) {
    std::string statusValue = status ? "START" : "STOP";
    std::string payload = std::string(type) + ";" + statusValue;
    sendPost("api/v1/brewtool/" + getMac() + "/electricity", payload);
}

void sendTemperatureStatisticToApi(float temperature) {
    sendPost("api/v1/brewtool/" + getMac() + "/temperature", std::to_string(temperature));
}