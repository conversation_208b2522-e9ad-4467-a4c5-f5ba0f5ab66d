#include <WiFi.h>
#include <Preferences.h>
#include <string.h>
#include <vector>

#define PREV_WIFI_LABEL "philun-wifi"

/*
  Constants
*/
char const *LABEL_WIFI_SSID = "ssid";
char const *LABEL_WIFI_PASSWORD = "password";

Preferences preferences;

extern bool hasInternet = false;

long lastReconnect = 0;

bool connectWifi() {
  // TODO: check if wifi is already connected
  // TODO: check if wifi is already connected to the right network
  preferences.begin(PREV_WIFI_LABEL, true);
  const String ssid = preferences.getString(LABEL_WIFI_SSID, "");
  const String password = preferences.getString(LABEL_WIFI_PASSWORD, "");
  preferences.end();

  if (WiFi.SSID() == ssid && WiFi.status() == WL_CONNECTED) {
    return true;
  }

  if (ssid != "" && password != "") {
    WiFi.begin(ssid.c_str(), password.c_str());
    for (int i = 0; i < 10; i++) {
      delay(500);
      if (WiFi.status() == WL_CONNECTED) {
        Serial.println("Connected to " + ssid);
        return true;
      }
    }
  }

  Serial.println("Not connected to " + ssid);
  return false;
};

void setupWifi() {
  WiFi.mode(WIFI_STA);
  WiFi.setAutoReconnect(true);
  hasInternet = connectWifi();
}

void onLoopWifi() {
  hasInternet = WiFi.status() == WL_CONNECTED;

  if (!hasInternet && lastReconnect == 0) {
    Serial.println("No internet connection");
    lastReconnect = millis();
  }

  if (!hasInternet && lastReconnect != 0 && millis() - lastReconnect > 600000) {
    lastReconnect = 0;

    hasInternet = connectWifi();
  }
}

const char* getWifiPassword() {
  preferences.begin(PREV_WIFI_LABEL, true);
  String password = preferences.getString(LABEL_WIFI_PASSWORD, "");
  preferences.end();
  return password.c_str();
};

bool setWifi(const char*  ssid, const char*  password) {
  preferences.begin(PREV_WIFI_LABEL);
  preferences.putString(LABEL_WIFI_SSID, String(ssid));
  preferences.putString(LABEL_WIFI_PASSWORD, String(password));
  preferences.end();
  return connectWifi();
};

const char* getWifiSsid() {
  preferences.begin(PREV_WIFI_LABEL, true);
  String ssid = preferences.getString(LABEL_WIFI_SSID, "");
  preferences.end();
  return ssid.c_str();
};

std::vector<std::string> scanWifiNetworks() {
  std::vector<std::string> ssid_list;
  return ssid_list;
}