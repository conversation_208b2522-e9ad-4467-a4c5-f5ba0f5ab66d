#include <vector>
// #include <string>
// #include "esp_wifi.h"
// #include "esp_event.h"
// #include "nvs_flash.h"
// #include "esp_log.h"
// #include <nvs.h>
// #include <nvs_flash.h>
// #include <string.h>

#include <WiFi.h>
#include <Preferences.h>
#include <string.h>

extern bool hasInternet;

bool connectWifi();

void setupWifi();

void onLoopWifi();

bool setWifi(const char* ssid, const char* password);

const char* getWifiPassword();

const char* getWifiSsid();

std::vector<std::string> scanWifiNetworks();