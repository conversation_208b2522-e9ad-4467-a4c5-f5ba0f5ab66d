// Ensure u32_t is defined before including esp_http_client.h
// #include <arch/cc.h>
// #include <IPAddress.h>
// #include "esp_http_client.h"
// #include "nvs_flash.h"
// #include "nvs.h"
// #include <cstring>
// #include <string>
// #include <sstream>
// #include <iomanip>
// #include <iostream>
// #include <vector>
#include <HTTPClient.h>
#include <DynamicWifi.hpp>
#include <Preferences.h>
#include <WiFiClientSecure.h>
#include <string.h>

#define HTTP_UNKNOW_ERROR "Unknown error"
#define HTTP_NOT_FOUND "Not found"
#define HTTP_SERVER_ERROR "Server error"

extern const uint8_t server_cert_pem_start[];
extern const uint8_t server_cert_pem_end[];

void sendPost(const std::string& route, const std::string& data);
std::string getData(const std::string& route);

// void onInitHttp();

void setApiKey(const std::string& newApiKey);

std::string getApiKey();

std::string getMac();