#include <HTTPClient.h>
#include <DynamicWifi.hpp>
#include <Preferences.h>
#include <WiFiClientSecure.h>
#include <string.h>
// #include <HTTPUpdate.h>

static char const *LABEL_API_KEY = "api-key";
static char const *LABEL_SERVER_CERTIFICATE = "server_certificate";

#define HTTP_UNKNOW_ERROR "Unknown error"
#define HTTP_NOT_FOUND "Not found"
#define HTTP_SERVER_ERROR "Server error"

#define OTA_UPDATE_URL "/firmware/latest.bin"

extern const uint8_t server_cert_pem_start[] asm("_binary_gaerschrank_philun_de_pem_start");
extern const uint8_t server_cert_pem_end[] asm("_binary_gaerschrank_philun_de_pem_end");

const char* PREF_LABEL_HTTP = "http";

WiFiClientSecure *client = new WiFiClientSecure;

void setApiKey(const std::string& newApiKey) {
  Preferences httpPreferences;
  httpPreferences.begin(PREF_LABEL_HTTP, false);
  httpPreferences.putString(LABEL_API_KEY, String(newApiKey.c_str()));
  httpPreferences.end();
}

std::string getApiKey() {
  Preferences httpPreferences;
  httpPreferences.begin(PREF_LABEL_HTTP, false);
  String apiKey = httpPreferences.getString(LABEL_API_KEY, DEFAULT_API_KEY);
  httpPreferences.end();
  return apiKey.c_str();
}

std::string mac2String(const uint8_t* ar) {
    char buf[18]; // 6*2 hex digits + 5 colons + null terminator
    snprintf(buf, sizeof(buf), "%02x:%02x:%02x:%02x:%02x:%02x",
             ar[0], ar[1], ar[2], ar[3], ar[4], ar[5]);
    return std::string(buf);
}

std::string getMac() {
    uint8_t mac[6];
    esp_efuse_mac_get_default(mac);
    return mac2String(mac);
}

// std::string mac2String(byte ar[]) {
//   std::string s;
//   for (byte i = 0; i < 6; ++i)
//   {
//     char buf[3];
//     sprintf(buf, "%02X", ar[i]); // J-M-L: slight modification, added the 0 in the format for padding 
//     s += buf;
//     if (i < 5) s += ':';
//   }
//   return s;
// }

// std::string getMac() {
//   return mac2String((byte*) ESP.getEfuseMac());
// }

void preHttpRequest(HTTPClient &http, std::string route) {
  std::string path = std::string(API_HOST) + "/" + route;
  String serverPath = String(path.c_str());
  http.begin(serverPath);
      
  // if (serverPath.startsWith("https")) {
  //   http.begin(serverPath);
  //   // http.setReuse(true);
  // } else {
  //   http.begin(serverPath);
  //   // For HTTP, set insecure (no certificate check)
  //   //http.setReuse(true); // Optional: reuse connection for HTTP
  // }

  Preferences httpPreferences;
  httpPreferences.begin(PREF_LABEL_HTTP, false);
  http.addHeader("X-ApiKey", httpPreferences.getString(LABEL_API_KEY, DEFAULT_API_KEY));
  httpPreferences.end();
  http.addHeader("X-Device", String(getMac().c_str()));

  http.addHeader("x-ESP32-STA-MAC", WiFi.macAddress());
  http.addHeader("x-ESP32-AP-MAC", WiFi.softAPmacAddress());

  // const char* headers[] = {HTTP_VERSION_HEADER};
  // http.collectHeaders(headers, sizeof(headers)/ sizeof(headers[0]));
}

// void postHttpRequest(HTTPClient &http) {
//   String receivedVersion = http.header(HTTP_VERSION_HEADER);
//   if (receivedVersion != VERSION && receivedVersion != "") {
//     // startFirmwareUpgrade();
//   }
// }

void sendPost(const std::string& route, const std::string& data) {
  client->setInsecure();
  HTTPClient http;

  preHttpRequest(http, route);

  // http.addHeader("Content-Type", "application/json");

  int httpResponseCode = http.POST(String(data.c_str()));

  Serial.println("Request: " + String(route.c_str()) + " => " + String(data.c_str()));
  Serial.println("HTTP Response code: " + String(httpResponseCode));

  if (httpResponseCode >= 300) {
      Serial.println("Error on sending POST: " + http.errorToString(httpResponseCode));
  }

  // postHttpRequest(http);
    
  http.end();
}

std::string getData(const std::string& route) {
  // WiFiClientSecure *client = new WiFiClientSecure;
  client->setInsecure();

  HTTPClient http;

  preHttpRequest(http, route);

  int httpResponseCode = http.GET();

  if (httpResponseCode < 0) {
      Serial.println("Error on HTTP request");
      http.end();
      return HTTP_UNKNOW_ERROR; // Ensure this constant is defined
  }

  if (httpResponseCode == 404) {
      Serial.println("404 Not Found");
      http.end();
      return HTTP_NOT_FOUND; // Or create a constant for "not found"
  }

  // Check for server errors
  if (httpResponseCode >= 500) {
      Serial.printf("Server error: %d\n", httpResponseCode);
      http.end();
      return HTTP_SERVER_ERROR; // Define this constant
  }

  String payload = http.getString();
  if (payload.length() == 0) {
      Serial.println("Received empty payload");
      http.end();
      return HTTP_UNKNOW_ERROR;
  }

  // postHttpRequest(http);

  // String payload = http.getString();

  Serial.println("Payload: " + payload);
  
  http.end();

  return payload.c_str();
}

// void checkAndUpdateFirmware(const std::string &newVersion) {
//   client->setInsecure();

//   HTTPClient http;
//   // if (WiFi.status() != WL_CONNECTED) {
//   //   Serial.println("WiFi not connected, skipping OTA update.");
//   //   return;
//   // }

//   http.addHeader("X-ApiKey", String(getApiKey().c_str()));

//   String currentVersion = "1.0.0";
  
//   std::string url = std::string(API_HOST) + OTA_UPDATE_URL;
//   Serial.println("Checking for OTA update at: " + String(url.c_str()));
//   // url.c_str()
//   t_httpUpdate_return ret = httpUpdate.update(http, currentVersion);
//   switch (ret) {
//     case HTTP_UPDATE_FAILED:
//       Serial.printf("HTTP_UPDATE_FAILED Error (%d): %s\n", httpUpdate.getLastError(), httpUpdate.getLastErrorString().c_str());
//       break;
//     case HTTP_UPDATE_NO_UPDATES:
//       Serial.println("HTTP_UPDATE_NO_UPDATES");
//       break;
//     case HTTP_UPDATE_OK:
//       Serial.println("HTTP_UPDATE_OK");
//       break;
//   }
// }