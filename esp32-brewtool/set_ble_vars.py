Import("env")

environment = env.subst("$PIOENV")

print("Current Env: ", environment)

import json
import os

print("Setting BLE variables")

prefix = "BLE_"

ble_path = '../shared/assets/ble.json'
manifest_path = './manifest.json'

def get_ble_vars():
    with open(ble_path) as f:
        d = json.load(f)
        print("Name: " + d["name"])
        return d

def set_general_ble_vars(d):
    name = prefix + "NAME"
    print("Added: " + name)
    env.Append(CPPDEFINES=[(name, env.StringifyMacro(d["name"]))])

def add_ble_characteristics(service):
    for characteristic in service["characteristics"]:
        add_ble_characteristic(characteristic)

def add_ble_characteristic(characteristic):
    name = prefix + "CHARACTERISTIC_" + characteristic["name"].upper() + '_UUID'
    print("Added: " + name)
    env.Append(CPPDEFINES=[(name, env.StringifyMacro(characteristic["uuid"]))])

def set_ble_services(d):
    for service in d["services"]:
        name = prefix + "SERVICE_" + service["name"].upper() + '_UUID'
        print("Added: " + name)
        env.Append(CPPDEFINES=[(name, env.StringifyMacro(service["uuid"]))])
        add_ble_characteristics(service)

def load_manifest():
    with open(manifest_path) as f:
        d = json.load(f)
        print("Version: " + d["version"])
        return d
    
def init_test_env():
    from dotenv import load_dotenv

    load_dotenv()

    if (environment == "test"):
        env.Append(CPPDEFINES=[
            ("WIFI_SSID", env.StringifyMacro(os.environ.get("WIFI_SSID"))), 
            ("WIFI_PASSWORD", env.StringifyMacro(os.environ.get("WIFI_PASSWORD")))
        ])


# Add BLE Variables to BUILD
d = get_ble_vars()
set_general_ble_vars(d)
set_ble_services(d)

# Add manifest data
manifest = load_manifest()
env.Append(CPPDEFINES=[
    ("VERSION", env.StringifyMacro(manifest["version"])),
    ("API_HOST", env.StringifyMacro(manifest["host"])),
])

# Testing
init_test_env()

