#include <DynamicWifi.hpp>
#include "NimBLEDevice.h"
#include <ArduinoJson.h>
#include <Http.hpp>

class PhilunBLEServer: public NimBLEServerCallbacks {
  void onConnect(NimBLEServer* pServer) {
  };

  void onDisconnect(NimBLEServer* pServer) {
    pServer->getAdvertising()->start();
  }
};

class BLEWifiChar: public NimBLECharacteristicCallbacks {
  void onWrite(NimBLECharacteristic *pCharacteristic) {
    std::string rxValue = pCharacteristic->getValue();

    if (rxValue.length() == 0)
    {
      pCharacteristic->setValue("{\"error\":\"INVALID_JSON\"}");
      return;
    }

    Serial.println("Wifi: " + String(rxValue.c_str()));

    JsonDocument request;
    auto error = deserializeJson(request, (char *)&rxValue[0]);
    if (error || !request["ssid"].is<String>() || !request["password"].is<String>()) {
      Serial.println("Received invalid JSON");
      pCharacteristic->setValue("{\"error\":\"INVALID_JSON\"}");
      return;
    }

    const String ssid = request["ssid"].as<String>();
    if (ssid.length() == 0)
    {
      Serial.println("Received invalid JSON");
      pCharacteristic->setValue("{\"error\":\"INVALID_JSON\"}");
      return;
    }

    std::string wifiSsid = request["ssid"].as<std::string>();
    std::string password = request["password"].as<std::string>();
    setWifi(wifiSsid.c_str(), password.c_str());
  }

  void onRead(NimBLECharacteristic *pCharacteristic) {
    JsonDocument doc;
    const String ssid = getWifiSsid();
    doc["ssid"] = ssid;
    doc["status"] = hasInternet;
    String json;
    serializeJson(doc, json);
    pCharacteristic->setValue(json);
  }
};

class BLEWifiScanChar: public BLECharacteristicCallbacks {
  void onRead(NimBLECharacteristic *pCharacteristic) {
    std::vector<std::string> ssids = scanWifiNetworks();
    if (ssids.size() == 0) {
      pCharacteristic->setValue("[]");
    } else {
      JsonDocument doc;
      JsonArray array = doc.to<JsonArray>();

      for (int i = 0; i < ssids.size(); ++i) {
        array.add(ssids[i]);
      }

      String res;
      serializeJson(doc, res);
      pCharacteristic->setValue(res);
    }
  }
};

class BLEApiKey: public BLECharacteristicCallbacks {
  void onWrite(NimBLECharacteristic *pCharacteristic) {
    setApiKey(pCharacteristic->getValue().c_str());
  }
};

void setupBLEWifi(NimBLEService* pService) {
  NimBLECharacteristic* pCharacteristic = pService->createCharacteristic(
    BLEUUID(BLE_CHARACTERISTIC_WIFI_UUID),
    NIMBLE_PROPERTY::READ   |
    NIMBLE_PROPERTY::READ_ENC |
    NIMBLE_PROPERTY::WRITE_NR |
    NIMBLE_PROPERTY::WRITE_ENC
  );
                    
  BLEWifiChar *pBleCallbacks = new BLEWifiChar();
  pCharacteristic->setCallbacks(pBleCallbacks);
}

void setupBLEWifiScan(NimBLEService* pService) {
  NimBLECharacteristic* pCharacteristic = pService->createCharacteristic(
    BLEUUID(BLE_CHARACTERISTIC_WIFI_SCAN_UUID),
    NIMBLE_PROPERTY::READ |
    NIMBLE_PROPERTY::READ_ENC
  );
    
  BLEWifiScanChar *pBleCallbacks = new BLEWifiScanChar();
  pCharacteristic->setCallbacks(pBleCallbacks);
}

void setupDeviceId(NimBLEService* pService) {
  NimBLECharacteristic* pCharacteristic = pService->createCharacteristic(
    BLEUUID(BLE_CHARACTERISTIC_DEVICE_ID_UUID),
    NIMBLE_PROPERTY::READ |
    NIMBLE_PROPERTY::READ_ENC
  );
    
  pCharacteristic->setValue(getMac());
}

void setupApiKey(NimBLEService* pService) {
  NimBLECharacteristic* pCharacteristic = pService->createCharacteristic(
    BLEUUID(BLE_CHARACTERISTIC_API_KEY_UUID),
    NIMBLE_PROPERTY::WRITE |
    NIMBLE_PROPERTY::WRITE_ENC
  );

  BLEApiKey *pBleCallbacks = new BLEApiKey();
  pCharacteristic->setCallbacks(pBleCallbacks);
}

void setupBluetoothLE() {
  NimBLEDevice::init(BLE_NAME);
  NimBLEDevice::setPower(ESP_PWR_LVL_P7);

  // Create the BLE Server
  NimBLEDevice::setSecurityIOCap(BLE_HS_IO_NO_INPUT_OUTPUT);
  NimBLEDevice::setSecurityRespKey(BLE_SM_PAIR_KEY_DIST_ENC |  BLE_SM_PAIR_KEY_DIST_ID);
  NimBLEDevice::setSecurityAuth(true, true, true);

  NimBLEServer *pServer = NimBLEDevice::createServer();
  pServer->setCallbacks(new PhilunBLEServer());

  NimBLEService *pService = pServer->createService(NimBLEUUID(BLE_SERVICE_GENERAL_UUID));

  setupBLEWifi(pService);
  setupBLEWifiScan(pService);
  setupDeviceId(pService);
  setupApiKey(pService);
  
  pService->start();
  
  // Start advertising
  NimBLEAdvertising *pAdvertising = pServer->getAdvertising();
  pAdvertising->addServiceUUID(NimBLEUUID(BLE_SERVICE_GENERAL_UUID));
  pAdvertising->start();
}