/*
  Board:    ESP32 Dev Module
*/
#include <Arduino.h>
#include <OneWire.h>
#include <DallasTemperature.h>
#include "BluethootApi.hpp"
#include <DynamicWifi.hpp>
#include <Api.hpp>

/*
  TODOs:
  - [ ] ota
  - [ ] offline help
  - [ ] update to new api & version
  - [ ] https://github.com/BernhardSchlegel/Brick-32
*/

/*
  Constants
*/
static int second = 1000;
static int minute = second * 60;
static int MaxMinusTemperature = -274;
static float TemperatureTolerance = 0.75;

/*
  Ports
*/
static int pinTempSensor = 26;
static int pinRelayHeating = 18;
static int pinRelayCooling = 19;

/*
  Falls beim Kompelieren ein <PERSON>hler 'void directModeInput(uint32_t)' kommt
  eine frühere Version von OneWire installieren
*/
OneWire oneWire(pinTempSensor);
DallasTemperature sensors(&oneWire);
/*
 Store
*/

// // Modus modus = FERMENTATION;
int powerOffLastTemp = 0;
long timestamp = 0;
int lastTemperature = MaxMinusTemperature;
// save last sensorTemperature, if internet is off
int destTemperature = MaxMinusTemperature;

// Track if temperature sensor is present
bool hasTempSensor = false;

void loadTime() {
  timestamp = loadTimeFromApi();
}

/*
  Schaltet das Relais ein oder aus
*/
void triggerRelay(int pin, bool status) {
  if (status) {
    digitalWrite(pin, HIGH);
  } else {
    digitalWrite(pin, LOW);
  }
}

/*
  Schaltet die Kühlung ein oder aus
 */
void setRelayCooling(bool status) {
  bool currentState = digitalRead(pinRelayCooling) == HIGH;
  if (status != currentState) {
    triggerRelay(pinRelayCooling, status);
    sendElectricalData("COOLING", status);
  }
}

/*
  Schaltet die Heizung ein oder aus
 */
void setRelayHeating(bool status) {
  bool currentState = digitalRead(pinRelayHeating) == HIGH;
  if (status != currentState) {
    triggerRelay(pinRelayHeating, status);
    sendElectricalData("HEATING", status);
  }
}

/*
  Schaltet die Heizung und Kühlung ein oder aus
 */
void triggerTemperatureHardware(float sensorTemperature, float destTemperature) {
  Serial.println("Temperature: " + String(sensorTemperature));
  if (sensorTemperature == 85) {
    Serial.println("Error with temperature sensor: send default temperature");
    return;
  }

  if (destTemperature == MaxMinusTemperature || sensorTemperature == -127) {
    Serial.println("Fermentation is off");
    setRelayCooling(false);
    setRelayHeating(false);
    return;
  }

  const float maxTemperature = destTemperature + TemperatureTolerance;
  const float minTemperature = destTemperature - TemperatureTolerance;

  bool relayHeatingState = digitalRead(pinRelayHeating) == HIGH;
  bool relayCoolingState = digitalRead(pinRelayCooling) == HIGH;

  if ((relayHeatingState && destTemperature > sensorTemperature) || (minTemperature > sensorTemperature)) {
    Serial.println("Temperature is too low");
    setRelayCooling(false);
    setRelayHeating(true);
  } else if ((relayCoolingState && destTemperature < sensorTemperature) || (maxTemperature < sensorTemperature)) {
    Serial.println("Temperature is too high");
    setRelayCooling(true);
    setRelayHeating(false);
  } else {
    Serial.println("Temperature is right");
    setRelayCooling(false);
    setRelayHeating(false);
  }
}

void sendTemperatureStatistic(float temperature, int destTemperature) {
  // only send every 5 minute if fermentation is not on
  if (destTemperature == MaxMinusTemperature) {
    float difference = millis() - powerOffLastTemp;
    if (difference < minute * 5) {
      return;
    } else {
      powerOffLastTemp = millis();
    }
  }

  if (temperature == -127) {
    // TODO: error with temperature sensor
    Serial.println("Error with temperature sensor: Temperature is -127");
    return;
  }

  if (temperature == 85) {
    Serial.println("Error with temperature sensor: Temperature is 85");
    return;
  }

  if (!hasInternet) {
    // TODO: save temperature in file system/preferences
    // sendTemperatureStatisticInOffline(temperature);
  } else {
    sendTemperatureStatisticToApi(temperature);
  }
}

void setup() {
  Serial.begin(115200);
  
  /*
    Init Temperature Sensor
  */
  sensors.begin();
  delay(100);

  // Check if temperature sensor is connected
  if (sensors.getDeviceCount() == 0) {
    Serial.println("ERROR: No DallasTemperature sensor detected!");
    hasTempSensor = false;
  } else {
    Serial.println("DallasTemperature sensor detected.");
    hasTempSensor = true;
  }

  setupBluetoothLE();
  setupWifi();

  /*
    Init Time
  */
  if (!hasInternet) {
    Serial.println("No internet connection, not getting time from api");
  } else {
    loadTime();
  }

  /*
    Init Relais
  */
  Serial.println("Init Relais");  
  pinMode(pinRelayCooling, OUTPUT);
  pinMode(pinRelayHeating, OUTPUT);
  Serial.println("Relais initialized");
}

void loop() {
  const int startTime = millis();
  Serial.println("Start Time set");

  onLoopWifi();

  if (hasInternet) {
    destTemperature = getDestinatedTemperature();
  }
  Serial.println("Destination temperature: " + String(destTemperature));

  float measuredTemperature = -127;
  if (hasTempSensor) {
    sensors.requestTemperatures();
    sleep(1);
    measuredTemperature = sensors.getTempCByIndex(0);
  }

  // send measured temperature to server
  sendTemperatureStatistic(measuredTemperature, destTemperature);

  // check for adjustment in temperature
  triggerTemperatureHardware(measuredTemperature, destTemperature);

  // // run every minute
  const int runTime = millis() - startTime;
  timestamp = timestamp + minute;
  const int delayTime = (second*10) - runTime;
  if (delayTime > 0) {
    delay(delayTime);
  } else {
    delay(1);
  }
}